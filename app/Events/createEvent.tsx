import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import {
  Platform,
  View,
  ScrollView,
  Pressable,
  TouchableOpacity,
  Keyboard,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Image,
} from 'react-native';
import { z } from 'zod';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Ionicons, MaterialIcons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import DatePicker from 'react-native-date-picker';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import * as Location from 'expo-location';
import ImagePicker from 'react-native-image-crop-picker';
import * as SecureStore from 'expo-secure-store';

import { Text } from '~/components/nativewindui/Text';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { useColorScheme } from '~/lib/useColorScheme';
import {
  FormControl,
  FormControlError,
  FormControlErrorText,
  FormControlLabel,
  FormControlLabelText,
} from '@/components/ui/form-control';
import { Button } from '@/components/ui/button';
import { Textarea, TextareaInput } from '@/components/ui/textarea';
import { Radio, RadioGroup, RadioIndicator, RadioLabel } from '@/components/ui/radio';
import LocationPicker, { LocationData } from '~/components/Map/LocationPicker';
import LocationPreview from '~/components/Map/LocationPreview';
import { useEventStore, UserStore } from '~/store/store';
import { Modal, ModalBackdrop, ModalContent, ModalBody } from '@/components/ui/modal';
import { EventService } from '~/services/EventService';
import { Toast } from 'toastify-react-native';

// Define event form schema with Zod
const eventFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  location: z.string().min(1, 'Location is required'),
  locationData: z
    .object({
      coordinates: z.tuple([z.number(), z.number()]),
      name: z.string(),
      address: z.string().optional(),
    })
    .optional(),
  coverImage: z.string().min(1, 'Cover image is required'),
  startDateTime: z.date(),
  endDateTime: z.date(),
  eventType: z.enum(['Leisure', 'Business', 'Education', 'Entertainment']),
  visibility: z.enum(['Public', 'Private']),
  isPaid: z.boolean(),
  ticketSetup: z.object({
    hasLevels: z.boolean(),
    totalTickets: z.number().optional(),
    price: z.number().optional(),
    levels: z
      .array(
        z.object({
          type: z.enum(['Basic', 'Premium', 'VIP']),
          quantity: z.number(),
          price: z.number(),
        })
      )
      .optional(),
  }),
});

type EventFormType = z.infer<typeof eventFormSchema>;

export default function CreateEventScreen() {
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [datePickerMode, setDatePickerMode] = useState<{
    type: 'start' | 'end' | null;
    open: boolean;
  }>({
    type: null,
    open: false,
  });
  const [ticketLevels, setTicketLevels] = useState<
    { type: 'Basic' | 'Premium' | 'VIP'; quantity: number; price: number }[]
  >([{ type: 'Basic', quantity: 0, price: 0 }]);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [coverImage, setCoverImage] = useState<string | null>(null);
  const [hasTickets, setHasTickets] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [disclaimerAccepted, setDisclaimerAccepted] = useState(false);

  // Bottom sheet refs
  const eventTypeSheetRef = useRef<BottomSheet>(null);
  const visibilitySheetRef = useRef<BottomSheet>(null);
  const ticketSetupSheetRef = useRef<BottomSheet>(null);
  const locationSheetRef = useRef<BottomSheet>(null);
  const currencySheetRef = useRef<BottomSheet>(null);
  const disclaimerSheetRef = useRef<BottomSheet>(null);

  // Location picker modal state
  const [locationModalVisible, setLocationModalVisible] = useState(false);
  const [isLocationPickerOpen, setIsLocationPickerOpen] = useState(false);

  // Bottom sheet snap points
  const eventTypeSnapPoints = useMemo(() => ['50%'], []);
  const visibilitySnapPoints = useMemo(() => ['35%'], []);
  const ticketSetupSnapPoints = useMemo(() => ['90%'], []);
  const locationSnapPoints = useMemo(() => ['93%'], []);
  const currencySnapPoints = useMemo(() => ['35%'], []);
  const disclaimerSnapPoints = useMemo(() => ['50%'], []);

  // Bottom sheet callbacks
  const openEventTypeSheet = useCallback(() => {
    eventTypeSheetRef.current?.expand();
  }, []);

  const openVisibilitySheet = useCallback(() => {
    visibilitySheetRef.current?.expand();
  }, []);

  const openTicketSetupSheet = useCallback(() => {
    ticketSetupSheetRef.current?.expand();
  }, []);

  const closeEventTypeSheet = useCallback(() => {
    eventTypeSheetRef.current?.close();
  }, []);

  const closeVisibilitySheet = useCallback(() => {
    visibilitySheetRef.current?.close();
  }, []);

  const closeTicketSetupSheet = useCallback(() => {
    // For paid events, sync multilevel ticket data with form
    if (isPaid) {
      setValue('ticketSetup.hasLevels', true);
      setValue('ticketSetup.levels', ticketLevels);
      // Clear single price since we're using multilevel
      setValue('ticketSetup.price', undefined);
      setValue('ticketSetup.totalTickets', undefined);
    } else if (hasTickets) {
      // For free events with tickets
      setValue('ticketSetup.hasLevels', false);
      setValue('ticketSetup.levels', []);
      setValue('ticketSetup.price', 0);
    } else {
      // For free events without tickets
      setValue('ticketSetup.hasLevels', false);
      setValue('ticketSetup.levels', []);
      setValue('ticketSetup.totalTickets', 0);
      setValue('ticketSetup.price', 0);
    }

    ticketSetupSheetRef.current?.close();
  }, [isPaid, hasTickets, ticketLevels, setValue]);
  const openLocationPicker = useCallback(() => {
    setIsLocationPickerOpen(true);
    locationSheetRef.current?.expand();
  }, []);

  const closeLocationPicker = useCallback(() => {
    setIsLocationPickerOpen(false);
    locationSheetRef.current?.close();
  }, []);

  const openCurrencySheet = useCallback(() => {
    currencySheetRef.current?.expand();
  }, []);

  const closeCurrencySheet = useCallback(() => {
    currencySheetRef.current?.close();
  }, []);

  const openDisclaimerSheet = useCallback(() => {
    disclaimerSheetRef.current?.expand();
  }, []);

  const closeDisclaimerSheet = useCallback(() => {
    disclaimerSheetRef.current?.close();
  }, []);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.5} />
    ),
    []
  );

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<EventFormType>({
    resolver: zodResolver(eventFormSchema),
    defaultValues: {
      title: '',
      description: '',
      location: '',
      locationData: undefined,
      coverImage: '',
      startDateTime: new Date(),
      endDateTime: new Date(new Date().getTime() + 3600000), // +1hr from now
      eventType: 'Leisure',
      visibility: 'Public',
      isPaid: false,
      ticketSetup: {
        hasLevels: false,
        totalTickets: 0,
        price: 0,
        levels: [],
      },
    },
  });

  const isPaid = watch('isPaid');
  const startDateTime = watch('startDateTime');
  const endDateTime = watch('endDateTime');
  const eventType = watch('eventType');
  const visibility = watch('visibility');

  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [eventCategories, setEventCategories] = useState([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const user = UserStore((state: any) => state.user);

  useEffect(() => {
    const checkDisclaimer = async () => {
      const accepted = await SecureStore.getItemAsync('disclaimerAccepted');
      setDisclaimerAccepted(accepted === 'true');
    };

    checkDisclaimer();
  }, []);

  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          console.log('Location permission denied');
          return;
        }

        const location = await Location.getCurrentPositionAsync({});
        setUserLocation([location.coords.longitude, location.coords.latitude]);
      } catch (error) {
        console.error('Error fetching user location:', error);
      }
    };

    fetchUserLocation();
  }, []);

  useEffect(() => {
    const fetchEventCategories = async () => {
      try {
        const categories = await EventService.getEventCategories();

        setEventCategories(categories.body);
        if (categories.body.length > 0) {
          setSelectedCategoryId(categories.body[0].id); // Default to the first category
        }
      } catch (error) {
        console.error('Error fetching event categories:', error);
      }
    };

    fetchEventCategories();
  }, []);

  const openDatePicker = (type: 'start' | 'end') => {
    setDatePickerMode({ type, open: true });
  };

  const closeDatePicker = () => {
    setDatePickerMode({ type: null, open: false });
  };

  const addTicketLevel = () => {
    if (ticketLevels.length < 3) {
      const nextLevel = ticketLevels.length === 1 ? 'Premium' : 'VIP';
      setTicketLevels([...ticketLevels, { type: nextLevel as any, quantity: 0, price: 0 }]);
    }
  };

  const removeTicketLevel = (index: number) => {
    if (ticketLevels.length > 1) {
      const newLevels = [...ticketLevels];
      newLevels.splice(index, 1);
      setTicketLevels(newLevels);
    }
  };

  const updateTicketLevel = (index: number, field: string, value: number) => {
    const newLevels = [...ticketLevels];
    newLevels[index] = { ...newLevels[index], [field]: value };
    setTicketLevels(newLevels);
  };

  const formatDate = (date: Date) => {
    return (
      date.toLocaleDateString() +
      ' ' +
      date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    );
  };

  const handleSelectLocation = (location: LocationData) => {
    setSelectedLocation(location);
    setValue('location', location?.manualAddress || '');
    setValue('locationData', location);
    closeLocationPicker();
  };

  const pickImage = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 400,
        height: 400,
        cropping: true,
        compressImageQuality: 0.8,
        includeBase64: false,
        freeStyleCropEnabled: true,
        mediaType: 'photo',
        cropperStatusBarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarColor: Platform.OS === 'android' ? colors.background : undefined,
        cropperToolbarWidgetColor: Platform.OS === 'android' ? colors.foreground : undefined,
        cropperActiveWidgetColor: Platform.OS === 'android' ? colors.primary : undefined,
        showCropGuidelines: Platform.OS === 'android' ? true : undefined,
        showCropFrame: Platform.OS === 'android' ? true : undefined,
        hideBottomControls: Platform.OS === 'android' ? false : undefined,
        enableRotationGesture: Platform.OS === 'android' ? true : undefined,
        disableCropperColorSetters: Platform.OS === 'android' ? false : undefined,
        // iOS-specific configurations
        cropperChooseColor: Platform.OS === 'ios' ? colors.primary : undefined,
        cropperCancelColor: Platform.OS === 'ios' ? colors.foreground : undefined,
        avoidEmptySpaceAroundImage: Platform.OS === 'ios' ? true : undefined,
        cropperToolbarTitle: 'Edit Photo',
      });

      setCoverImage(image.path);
      setValue('coverImage', image.path);
    } catch (error: any) {
      if (error.code !== 'E_PICKER_CANCELLED') {
        console.log('Error picking image:', error);
      }
    }
  };

  const router = useRouter();

  const formatDateForAPI = (date: Date) => {
    const pad = (num: number) => String(num).padStart(2, '0');
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}T${pad(
      date.getHours()
    )}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
  };

  const onSubmit = async (data: EventFormType) => {
    try {
      setIsLoading(true); // Set loading to true
      const formData = new FormData();

      if (coverImage) {
        formData.append('coverImages', {
          uri: coverImage,
          name: 'coverImage.jpg',
          type: 'image/jpeg',
        } as any);
      }

      formData.append('eventCategoryId', selectedCategoryId || '');
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('location', data.location);
      formData.append('startDateTime', formatDateForAPI(data.startDateTime)); // Format date
      formData.append('endDateTime', formatDateForAPI(data.endDateTime)); // Format date
      formData.append('isPaid', data.isPaid.toString());
      formData.append('owners', ''); // Hardcoded for now
      formData.append('userId', user.id);
      formData.append('currency', selectedCurrency); // Use selected currency
      formData.append('visibility', data.visibility.toUpperCase());
      formData.append('locationData', JSON.stringify(data.locationData));
      formData.append('ticketSetup', JSON.stringify(data.ticketSetup));

      const response = await EventService.createEvent(formData);
      (useEventStore.getState() as { setEventData: (eventData) => void }).setEventData(
        response.body
      );

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Event Created',
          text2: 'Your event has been created successfully.',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
        router.replace({ pathname: '/Events/eventCreated' });
      }
      // router.replace({ pathname: '/Events/eventCreated' });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Event Creation Failed',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsLoading(false); // Set loading to false
    }
  };

  const visibilityOptions = [
    { label: 'Public', value: 'Public' },
    { label: 'Private', value: 'Private' },
  ];

  const getIconColor = () => (isDark ? '#d1d5db' : '#6b7280');

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const selectedCategory = eventCategories.find((category) => category.id === selectedCategoryId);

  return (
    <>
      <StatusBar
        style={Platform.OS === 'ios' ? 'light' : colorScheme === 'dark' ? 'light' : 'dark'}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
        <ScrollView
          className={'flex-1'}
          style={{ backgroundColor: colors.background }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{ flexGrow: 1 }}>
          <View className="items-center flex-1 gap-4 p-4">
            {/* Event Cover Image */}
            <View className="items-center w-full mb-2">
              <TouchableOpacity
                onPress={pickImage}
                className={`mb-2 h-32 w-full items-center  justify-center overflow-hidden rounded-2xl`}
                style={{ backgroundColor: colors.grey5 }}>
                {coverImage ? (
                  <Image
                    source={{ uri: coverImage }}
                    className="w-full h-full"
                    resizeMode="cover"
                  />
                ) : (
                  <View className="flex items-center justify-center">
                    <MaterialCommunityIcons
                      name="image-plus"
                      size={40}
                      color={isDark ? '#6b7280' : '#9ca3af'}
                    />
                    <Text
                      className={`mt-2 text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      Add Event Cover Image
                    </Text>
                  </View>
                )}
              </TouchableOpacity>

              {coverImage && (
                <TouchableOpacity
                  onPress={() => {
                    setCoverImage(null);
                    setValue('coverImage', '');
                  }}
                  className="mt-1 mb-2">
                  <Text className={`${isDark ? 'text-red-400' : 'text-red-500'} text-base`}>
                    Remove Image
                  </Text>
                </TouchableOpacity>
              )}

              {/* Cover Image Validation Error */}
              {errors.coverImage && (
                <View className="mt-1">
                  <Text className="text-sm font-medium text-red-500">
                    {errors.coverImage.message}
                  </Text>
                </View>
              )}
            </View>

            <View className="w-full">
              {/* Event Title */}
              <Controller
                control={control}
                name="title"
                render={({ field: { value, onChange } }) => (
                  <FormControl isInvalid={!!errors.title} className="mb-4">
                    <Input
                      className={`h-14 rounded-xl border-0 `}
                      style={{ backgroundColor: colors.grey5 }}>
                      <InputField
                        value={value}
                        onChangeText={onChange}
                        placeholder="Enter event title"
                        returnKeyType="done"
                        onSubmitEditing={dismissKeyboard}
                        className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                        placeholderTextColor={isDark ? colors.grey : colors.grey}
                      />
                    </Input>
                    {errors.title && (
                      <FormControlError>
                        <FormControlErrorText className="text-red-500">
                          {errors.title.message}
                        </FormControlErrorText>
                      </FormControlError>
                    )}
                  </FormControl>
                )}
              />

              {/* Event Description */}
              <Controller
                control={control}
                name="description"
                render={({ field: { value, onChange } }) => (
                  <FormControl isInvalid={!!errors.description} className="mb-4">
                    <Textarea
                      size="md"
                      className={`h-20  rounded-xl border-0 `}
                      style={{ backgroundColor: colors.grey5 }}>
                      <TextareaInput
                        value={value}
                        onChangeText={onChange}
                        placeholder="Describe your event"
                        returnKeyType="done"
                        style={{
                          color: colors.foreground,
                        }}
                        blurOnSubmit
                        onSubmitEditing={dismissKeyboard}
                        className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                        placeholderTextColor={isDark ? colors.grey : colors.grey}
                      />
                    </Textarea>
                    {errors.description && (
                      <FormControlError>
                        <FormControlErrorText className="text-red-500">
                          {errors.description.message}
                        </FormControlErrorText>
                      </FormControlError>
                    )}
                  </FormControl>
                )}
              />

              {/* Event Location */}
              <FormControl isInvalid={!!errors.location} className="mb-4">
                {selectedLocation && !isLocationPickerOpen ? (
                  <View className="mb-2">
                    <LocationPreview location={selectedLocation} />
                    <TouchableOpacity
                      onPress={openLocationPicker}
                      className="flex-row items-center justify-center mt-2">
                      <Text
                        className={`${isDark ? 'text-violet-400' : 'text-violet-600'} font-medium`}>
                        Change Event Location
                      </Text>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <Pressable
                    onPress={openLocationPicker}
                    className={`flex-row items-center justify-between rounded-xl p-3.5 `}
                    style={{ backgroundColor: colors.grey5 }}>
                    <Text className={`font-medium`} style={{ color: colors.foreground }}>
                      Choose Event location
                    </Text>
                    <MaterialIcons name="location-on" size={20} color={getIconColor()} />
                  </Pressable>
                )}

                {errors.location && (
                  <FormControlError>
                    <FormControlErrorText className="font-medium text-red-500">
                      {errors.location.message}
                    </FormControlErrorText>
                  </FormControlError>
                )}
              </FormControl>

              {/* Combined Date & Time Section */}
              <View className="mb-4">
                <View className={`rounded-xl p-4`} style={{ backgroundColor: colors.grey5 }}>
                  <View className="flex-row items-center justify-between mb-3">
                    <View className="flex-row items-center">
                      <MaterialCommunityIcons name="clock-start" size={20} color={getIconColor()} />
                      <Text className={`ml-2 font-medium`} style={{ color: colors.grey }}>
                        Starts
                      </Text>
                    </View>
                    <Pressable
                      onPress={() => openDatePicker('start')}
                      className={`rounded-lg px-3 py-1 ${isDark ? 'bg-gray-700' : 'bg-white'}`}>
                      <Text className={'font-medium'} style={{ color: colors.foreground }}>
                        {formatDate(startDateTime)}
                      </Text>
                    </Pressable>
                  </View>

                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                      <MaterialCommunityIcons name="clock-end" size={20} color={getIconColor()} />
                      <Text className={`ml-2 font-medium`} style={{ color: colors.grey }}>
                        Ends
                      </Text>
                    </View>
                    <Pressable
                      onPress={() => openDatePicker('end')}
                      className={`rounded-lg px-3 py-1 ${isDark ? 'bg-gray-700' : 'bg-white'}`}>
                      <Text className={'font-medium'} style={{ color: colors.foreground }}>
                        {formatDate(endDateTime)}
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </View>

              <DatePicker
                modal
                open={datePickerMode.open}
                date={datePickerMode.type === 'start' ? startDateTime : endDateTime}
                onConfirm={(date) => {
                  if (datePickerMode.type === 'start') {
                    setValue('startDateTime', date);
                    // Ensure end time is after start time
                    if (date > endDateTime) {
                      setValue('endDateTime', new Date(date.getTime() + 3600000)); // +1hr
                    }
                  } else if (datePickerMode.type === 'end') {
                    setValue('endDateTime', date);
                  }
                  closeDatePicker();
                }}
                onCancel={closeDatePicker}
                mode="datetime"
                minimumDate={datePickerMode.type === 'end' ? startDateTime : undefined}
              />

              {/* Event Type and Visibility - Side by Side */}
              <View className="flex-row gap-4 mb-4">
                <View className="flex-1">
                  <Text
                    className={`mb-2 font-medium text-base `}
                    style={{ color: colors.foreground }}>
                    Event Type
                  </Text>
                  <Pressable
                    onPress={openEventTypeSheet}
                    className={`flex-row items-center justify-between rounded-xl p-3.5`}
                    style={{ backgroundColor: colors.grey5 }}>
                    <View className="flex-row items-center">
                      <MaterialCommunityIcons
                        name="tag-outline"
                        size={20}
                        color={getIconColor()}
                        className="mr-2"
                      />
                      <Text className={'font-medium'} style={{ color: colors.grey }}>
                        {selectedCategory ? selectedCategory.categoryName : 'Select Event Type'}
                      </Text>
                    </View>
                    <Ionicons name="chevron-up" size={20} color={getIconColor()} />
                  </Pressable>
                </View>

                <View className="flex-1">
                  <Text
                    className={`mb-2 font-medium text-base `}
                    style={{ color: colors.foreground }}>
                    Visibility
                  </Text>
                  <Pressable
                    onPress={openVisibilitySheet}
                    className={`flex-row items-center justify-between rounded-xl p-3.5 `}
                    style={{ backgroundColor: colors.grey5 }}>
                    <View className="flex-row items-center">
                      <MaterialIcons
                        name={visibility === 'Private' ? 'lock' : 'public'}
                        size={20}
                        color={getIconColor()}
                        className="mr-2"
                      />
                      <Text className={'font-medium'} style={{ color: colors.grey }}>
                        {visibility}
                      </Text>
                    </View>
                    <Ionicons name="chevron-up" size={20} color={getIconColor()} />
                  </Pressable>
                </View>
              </View>

              {/* Paid/Free Radio Button */}
              <Controller
                control={control}
                name="isPaid"
                render={({ field: { value, onChange } }) => (
                  <FormControl className="mb-4">
                    <FormControlLabel>
                      <FormControlLabelText
                        className={`font-medium text-base `}
                        style={{ color: colors.foreground }}>
                        Event Ticketing
                      </FormControlLabelText>
                    </FormControlLabel>
                    <RadioGroup
                      value={value ? 'paid' : 'free'}
                      onChange={(radioValue) => {
                        const isPaid = radioValue === 'paid';
                        if (isPaid && !disclaimerAccepted) {
                          // Show disclaimer when selecting paid
                          openDisclaimerSheet();
                          return;
                        }
                        onChange(isPaid);
                        if (!isPaid) {
                          // Reset price to 0 when switching to free
                          setValue('ticketSetup.price', 0);
                        }
                        // Always enable tickets option by default when changing
                        setHasTickets(true);
                      }}>
                      <View className="flex-row gap-6 mt-1">
                        <Radio value="free">
                          <RadioIndicator
                            style={{ backgroundColor: !isPaid ? colors.primary : colors.grey }}
                          />
                          <RadioLabel
                            className={`ml-2 font-medium `}
                            style={{ color: colors.grey }}>
                            Free Event
                          </RadioLabel>
                        </Radio>
                        <Radio value="paid">
                          <RadioIndicator
                            style={{ backgroundColor: isPaid ? colors.primary : colors.grey }}
                          />
                          <RadioLabel
                            className={`ml-2 font-medium `}
                            style={{ color: colors.grey }}>
                            Paid Event
                          </RadioLabel>
                        </Radio>
                      </View>
                    </RadioGroup>
                  </FormControl>
                )}
              />

              {/* Ticket Setup Button - Opens Bottom Sheet (for both free and paid events) */}
              <Button
                size="md"
                variant="outline"
                className={`mb-4 h-14 rounded-xl  border-0`}
                style={{ backgroundColor: colors.grey5 }}
                onPress={openTicketSetupSheet}>
                <FontAwesome5 name="ticket-alt" size={16} color={getIconColor()} className="mr-2" />
                <Text className={'font-medium'} style={{ color: colors.grey }}>
                  Configure Ticket Setup
                </Text>
              </Button>

              {/* Currency Selection */}
              <View className="mb-4">
                <Text
                  className={`mb-2 font-medium text-base `}
                  style={{ color: colors.foreground }}>
                  Currency
                </Text>
                <Pressable
                  onPress={openCurrencySheet}
                  className={`flex-row items-center justify-between rounded-xl p-3.5`}
                  style={{ backgroundColor: colors.grey5 }}>
                  <View className="flex-row items-center">
                    <MaterialCommunityIcons
                      name="currency-usd"
                      size={20}
                      color={getIconColor()}
                      className="mr-2"
                    />
                    <Text className={'font-medium'} style={{ color: colors.grey }}>
                      {selectedCurrency}
                    </Text>
                  </View>
                  <Ionicons name="chevron-up" size={20} color={getIconColor()} />
                </Pressable>
              </View>

              {/* Create Event Button */}
              <Button
                size="lg"
                variant="solid"
                className={`mb-10 mt-3 h-14 rounded-xl font-bold ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
                onPress={handleSubmit(onSubmit)}
                disabled={isLoading}>
                {isLoading ? (
                  <Text className="font-semibold text-white">Creating...</Text>
                ) : (
                  <Text className="font-semibold text-white">Create Event</Text>
                )}
              </Button>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Event Type Bottom Sheet */}
      <BottomSheet
        ref={eventTypeSheetRef}
        index={-1}
        snapPoints={eventTypeSnapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView>
          <View className="px-4 pb-8">
            <View className="pb-2 mb-4">
              <Text
                className={`text-center font-bold text-xl ${
                  isDark ? 'text-white' : 'text-gray-800'
                }`}>
                Select Event Type
              </Text>
            </View>
            {eventCategories.map((category) => (
              <Pressable
                key={category.id}
                onPress={() => {
                  setSelectedCategoryId(category.id);
                  closeEventTypeSheet();
                }}
                className={`flex-row items-center border-b ${
                  isDark ? 'border-gray-700' : 'border-gray-200'
                } py-4`}>
                <MaterialCommunityIcons
                  name="tag-outline"
                  size={20}
                  color={getIconColor()}
                  style={{ marginRight: 10 }}
                />
                <Text
                  className={`${isDark ? 'text-white' : 'text-gray-800'} font-medium text-base ${
                    selectedCategoryId === category.id ? 'font-bold' : ''
                  }`}>
                  {category.categoryName}
                </Text>
                {selectedCategoryId === category.id && (
                  <Ionicons
                    name="checkmark-circle"
                    size={22}
                    color={isDark ? '#8b5cf6' : '#7c3aed'}
                    style={{ marginLeft: 'auto' }}
                  />
                )}
              </Pressable>
            ))}
          </View>
        </BottomSheetView>
      </BottomSheet>

      {/* Visibility Bottom Sheet */}
      <BottomSheet
        ref={visibilitySheetRef}
        index={-1}
        snapPoints={visibilitySnapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView>
          <View className="px-4 pb-8">
            <View className="pb-2 mb-4 ">
              <Text
                className={`text-center font-bold text-xl ${
                  isDark ? 'text-white' : 'text-gray-800'
                }`}>
                Select Visibility
              </Text>
            </View>
            {visibilityOptions.map((option) => (
              <Pressable
                key={option.value}
                onPress={() => {
                  setValue('visibility', option.value as any);
                  closeVisibilitySheet();
                }}
                className={`flex-row items-center border-b ${
                  isDark ? 'border-gray-700' : 'border-gray-200'
                } py-4`}>
                <MaterialIcons
                  name={option.value === 'Private' ? 'lock' : 'public'}
                  size={22}
                  color={getIconColor()}
                  style={{ marginRight: 12 }}
                />
                <Text
                  className={`${isDark ? 'text-white' : 'text-gray-800'} font-medium text-base ${
                    visibility === option.value ? 'font-bold' : ''
                  }`}>
                  {option.label}
                </Text>
                {visibility === option.value && (
                  <Ionicons
                    name="checkmark-circle"
                    size={22}
                    color={isDark ? '#8b5cf6' : '#7c3aed'}
                    style={{ marginLeft: 'auto' }}
                  />
                )}
              </Pressable>
            ))}
          </View>
        </BottomSheetView>
      </BottomSheet>

      {/* Location Picker Modal */}
      <Modal isOpen={locationModalVisible} onClose={closeLocationPicker} size="full">
        <ModalBackdrop />
        <ModalContent
          style={{
            backgroundColor: colors.background,
            margin: 0,
            maxWidth: '100%',
            height: '100%',
          }}>
          <ModalBody style={{ padding: 0, flex: 1 }}>
            <LocationPicker
              onClose={closeLocationPicker}
              onSelectLocation={handleSelectLocation}
              initialLocation={selectedLocation || undefined}
              userLocation={userLocation || undefined}
            />
          </ModalBody>
        </ModalContent>
      </Modal>

      <BottomSheet
        ref={locationSheetRef}
        index={-1}
        snapPoints={['100%']}
        enablePanDownToClose={false}
        enableContentPanningGesture={false}
        enableHandlePanningGesture={false}
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}
        android_keyboardInputMode="adjustResize"
        style={{ borderRadius: 0, backgroundColor: 'transparent' }}>
        <BottomSheetView style={{ flex: 1 }}>
          <View
            className="flex-row items-center justify-between px-4 py-2 border-b"
            style={{ borderBottomColor: isDark ? '#374151' : '#e5e7eb' }}>
            <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
              Select Location
            </Text>
            <TouchableOpacity onPress={closeLocationPicker} className="p-2">
              <Ionicons name="close" size={24} color={getIconColor()} />
            </TouchableOpacity>
          </View>
          <LocationPicker
            onClose={closeLocationPicker}
            onSelectLocation={handleSelectLocation}
            initialLocation={selectedLocation || undefined}
            userLocation={userLocation || undefined}
          />
        </BottomSheetView>
      </BottomSheet>

      {/* Ticket Setup Bottom Sheet */}
      <BottomSheet
        ref={ticketSetupSheetRef}
        index={-1}
        snapPoints={ticketSetupSnapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView>
          <ScrollView className="px-4 pb-8">
            <View className="pb-2 mb-4 ">
              <Text
                className={`text-center font-bold text-xl ${
                  isDark ? 'text-white' : 'text-gray-800'
                }`}>
                Ticket Setup
              </Text>
            </View>

            {/* Free event ticket configuration */}
            {!isPaid && (
              <FormControl className="mb-4">
                <FormControlLabel>
                  <FormControlLabelText
                    className={`font-medium text-base `}
                    style={{ color: colors.grey }}>
                    Ticket Options
                  </FormControlLabelText>
                </FormControlLabel>
                <RadioGroup
                  value={hasTickets ? 'tickets' : 'noTickets'}
                  onChange={(value) => setHasTickets(value === 'tickets')}>
                  <View className="flex-row gap-6 mt-1 mb-4">
                    <Radio value="noTickets">
                      <RadioIndicator
                        style={{ backgroundColor: !hasTickets ? colors.primary : colors.grey }}
                      />
                      <RadioLabel
                        className={`ml-2 font-medium`}
                        style={{ color: colors.foreground }}>
                        No Tickets Required
                      </RadioLabel>
                    </Radio>
                    <Radio value="tickets">
                      <RadioIndicator
                        style={{ backgroundColor: hasTickets ? colors.primary : colors.grey }}
                      />
                      <RadioLabel
                        className={`ml-2 font-medium`}
                        style={{ color: colors.foreground }}>
                        Free Tickets
                      </RadioLabel>
                    </Radio>
                  </View>
                </RadioGroup>
              </FormControl>
            )}

            {/* Ticket quantity for free events */}
            {!isPaid && hasTickets && (
              <Controller
                control={control}
                name="ticketSetup.totalTickets"
                render={({ field: { value, onChange } }) => (
                  <FormControl className="mb-4">
                    <FormControlLabel>
                      <FormControlLabelText
                        className={`font-medium text-base `}
                        style={{ color: colors.foreground }}>
                        Total Free Tickets
                      </FormControlLabelText>
                    </FormControlLabel>
                    <Input
                      variant="outline"
                      size="md"
                      className={`h-14 rounded-xl border-0 ${isDark ? 'bg-gray-700' : 'bg-[#f1edf1]'}`}>
                      <InputField
                        keyboardType="numeric"
                        value={value?.toString() || ''}
                        onChangeText={(text) => onChange(parseInt(text) || 0)}
                        placeholder="Enter ticket quantity"
                        returnKeyType="done"
                        onSubmitEditing={dismissKeyboard}
                        className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                        placeholderTextColor={isDark ? colors.grey : colors.grey}
                      />
                    </Input>
                  </FormControl>
                )}
              />
            )}

            {/* Paid tickets with multiple tiers only */}
            {isPaid && (
              <View className="gap-4">
                {ticketLevels.map((level, index) => (
                  <View
                    key={index}
                    className={`mb-3 gap-2 rounded-xl p-4 ${
                      isDark ? 'bg-gray-700' : 'bg-[#f1edf1]'
                    }`}>
                    <View className="flex-row items-center justify-between mb-2">
                      <View className="flex-row items-center">
                        <MaterialCommunityIcons
                          name="ticket-confirmation"
                          size={18}
                          color={getIconColor()}
                        />
                        <Text className={`ml-2 font-semibold ${isDark ? 'text-white' : ''}`}>
                          {level.type} Ticket
                        </Text>
                      </View>
                      {ticketLevels.length > 1 && (
                        <TouchableOpacity onPress={() => removeTicketLevel(index)} className="p-1">
                          <MaterialCommunityIcons name="delete-outline" size={20} color="#ef4444" />
                        </TouchableOpacity>
                      )}
                    </View>
                    <View className="flex-row gap-3">
                      <View className="flex-1">
                        <Text
                          className={`mb-1 font-medium text-base`}
                          style={{ color: colors.foreground }}>
                          Quantity
                        </Text>
                        <Input
                          variant="outline"
                          size="md"
                          className={`h-14 rounded-lg border-0 `}
                          style={{ backgroundColor: colors.grey5 }}>
                          <InputField
                            keyboardType="numeric"
                            value={level.quantity.toString()}
                            onChangeText={(text) =>
                              updateTicketLevel(index, 'quantity', parseInt(text) || 0)
                            }
                            placeholder="Quantity"
                            placeholderTextColor={isDark ? colors.grey : colors.grey}
                            className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                            returnKeyType="done"
                            onSubmitEditing={dismissKeyboard}
                          />
                        </Input>
                      </View>
                      <View className="flex-1">
                        <Text
                          className={`mb-1 font-medium text-base `}
                          style={{ color: colors.foreground }}>
                          Price
                        </Text>
                        <Input
                          variant="outline"
                          size="md"
                          className={`h-14 rounded-lg border-0 `}
                          style={{ backgroundColor: colors.grey5 }}>
                          <InputField
                            keyboardType="decimal-pad"
                            value={level.price.toString()}
                            onChangeText={(text) =>
                              updateTicketLevel(index, 'price', parseFloat(text) || 0)
                            }
                            placeholder="Price"
                            placeholderTextColor={isDark ? colors.grey : colors.grey}
                            className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                            returnKeyType="done"
                            onSubmitEditing={dismissKeyboard}
                          />
                          <InputSlot className="pr-3">
                            <Text className={isDark ? 'text-gray-300' : ''}>$</Text>
                          </InputSlot>
                        </Input>
                      </View>
                    </View>
                  </View>
                ))}
                {ticketLevels.length < 3 && (
                  <Button
                    variant="outline"
                    size="md"
                    onPress={addTicketLevel}
                    className={`mt-2 h-14 rounded-xl  border-dashed  ${
                      isDark ? 'border-gray-600' : 'border-gray-300'
                    }`}>
                    <Ionicons name="add" size={20} color={getIconColor()} />
                    <Text className={`ml-2 font-bold`} style={{ color: colors.grey }}>
                      Add Ticket Level
                    </Text>
                  </Button>
                )}
              </View>
            )}

            <Button
              size="md"
              variant="solid"
              className={`mt-4 h-14 rounded-xl font-bold  ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
              onPress={closeTicketSetupSheet}>
              <Text className="font-bold text-white">Done</Text>
            </Button>
          </ScrollView>
        </BottomSheetView>
      </BottomSheet>

      {/* Currency Bottom Sheet */}
      <BottomSheet
        ref={currencySheetRef}
        index={-1}
        snapPoints={currencySnapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView>
          <View className="px-4 pb-8">
            <View className="pb-2 mb-4">
              <Text
                className={`text-center font-bold text-xl ${
                  isDark ? 'text-white' : 'text-gray-800'
                }`}>
                Select Currency
              </Text>
            </View>
            <Pressable
              onPress={() => {
                setSelectedCurrency('USD');
                closeCurrencySheet();
              }}
              className={`flex-row items-center border-b ${
                isDark ? 'border-gray-700' : 'border-gray-200'
              } py-4`}>
              <MaterialCommunityIcons
                name="currency-usd"
                size={20}
                color={getIconColor()}
                style={{ marginRight: 10 }}
              />
              <Text
                className={`${isDark ? 'text-white' : 'text-gray-800'} font-medium text-base ${
                  selectedCurrency === 'USD' ? 'font-bold' : ''
                }`}>
                USD
              </Text>
              {selectedCurrency === 'USD' && (
                <Ionicons
                  name="checkmark-circle"
                  size={22}
                  color={isDark ? '#8b5cf6' : '#7c3aed'}
                  style={{ marginLeft: 'auto' }}
                />
              )}
            </Pressable>
          </View>
        </BottomSheetView>
      </BottomSheet>

      <BottomSheet
        ref={disclaimerSheetRef}
        index={-1}
        snapPoints={disclaimerSnapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={{
          backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
          width: 40,
        }}
        backgroundStyle={{
          backgroundColor: colors.background,
        }}>
        <BottomSheetView>
          <View className="px-4 pb-8">
            <View className="pb-2 mb-4">
              <Text
                className={`text-center font-bold text-xl ${
                  isDark ? 'text-white' : 'text-gray-800'
                }`}>
                Payment Terms
              </Text>
            </View>
            <Text
              className={`mb-6 text-base ${
                isDark ? 'text-gray-300' : 'text-gray-700'
              } text-center leading-6`}>
              By using this platform to sell tickets, you agree that 90% of the ticket revenue will
              be transferred to you. The remaining 10% is retained as processing and service fees.
            </Text>

            <View className="flex-row gap-3">
              <Button
                size="md"
                variant="outline"
                className={`h-14 flex-1 rounded-xl border-2 ${
                  isDark ? 'border-gray-600' : 'border-gray-300'
                }`}
                onPress={closeDisclaimerSheet}>
                <Text className={`font-bold ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Cancel
                </Text>
              </Button>

              <Button
                size="md"
                variant="solid"
                className={`h-14 flex-1 rounded-xl font-bold ${
                  isDark ? 'bg-violet-700' : 'bg-violet-600'
                }`}
                onPress={async () => {
                  setDisclaimerAccepted(true);
                  await SecureStore.setItemAsync('disclaimerAccepted', 'true');
                  setValue('isPaid', true);
                  setHasTickets(true);
                  closeDisclaimerSheet();
                }}>
                <Text className="font-bold text-white">I Accept</Text>
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </>
  );
}
