import axios from 'axios';

export interface PaymentStatus {
  reference: string;
  paynowreference: string;
  amount: string;
  status: 'Created' | 'Sent' | 'Paid';
  pollurl: string;
  hash: string;
}

export interface PaymentResult {
  success: boolean;
  status: 'Paid' | 'Failed' | 'Timeout';
  amount?: string;
  reference?: string;
}

export class PaymentPollingService {
  private static parsePaymentResponse(responseText: string): PaymentStatus | null {
    try {
      // Parse the URL-encoded response
      const params = new URLSearchParams(responseText);
      
      return {
        reference: params.get('reference') || '',
        paynowreference: params.get('paynowreference') || '',
        amount: params.get('amount') || '',
        status: (params.get('status') as 'Created' | 'Sent' | 'Paid') || 'Created',
        pollurl: decodeURIComponent(params.get('pollurl') || ''),
        hash: params.get('hash') || '',
      };
    } catch (error) {
      console.error('Error parsing payment response:', error);
      return null;
    }
  }

  private static async pollPaymentStatus(pollUrl: string): Promise<PaymentStatus | null> {
    try {
      const response = await axios.get(pollUrl, {
        timeout: 10000, // 10 second timeout
      });
      
      return this.parsePaymentResponse(response.data);
    } catch (error) {
      console.error('Error polling payment status:', error);
      return null;
    }
  }

  /**
   * Polls payment status until completion or timeout
   * @param pollUrl - The URL to poll for payment status
   * @param maxAttempts - Maximum number of polling attempts (default: 60)
   * @param intervalMs - Interval between polls in milliseconds (default: 3000)
   * @returns Promise<PaymentResult>
   */
  static async pollUntilComplete(
    pollUrl: string,
    maxAttempts: number = 60, // 3 minutes total
    intervalMs: number = 3000 // 3 seconds between polls
  ): Promise<PaymentResult> {
    let attempts = 0;
    let lastStatus: 'Created' | 'Sent' | 'Paid' = 'Created';
    let amount = '';
    let reference = '';

    while (attempts < maxAttempts) {
      const status = await this.pollPaymentStatus(pollUrl);
      
      if (!status) {
        attempts++;
        await new Promise(resolve => setTimeout(resolve, intervalMs));
        continue;
      }

      amount = status.amount;
      reference = status.reference;
      
      console.log(`Payment polling attempt ${attempts + 1}: Status = ${status.status}`);

      // Track status progression
      if (status.status === 'Paid') {
        return {
          success: true,
          status: 'Paid',
          amount,
          reference,
        };
      }

      // Check for failed transaction (Created -> Sent -> Created)
      if (lastStatus === 'Sent' && status.status === 'Created') {
        return {
          success: false,
          status: 'Failed',
          amount,
          reference,
        };
      }

      lastStatus = status.status;
      attempts++;
      
      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }

    // Timeout reached
    return {
      success: false,
      status: 'Timeout',
      amount,
      reference,
    };
  }

  /**
   * Starts polling and returns a promise that resolves when payment is complete
   * Also provides a cancel function to stop polling
   */
  static startPolling(
    pollUrl: string,
    onStatusUpdate?: (status: PaymentStatus) => void
  ): {
    promise: Promise<PaymentResult>;
    cancel: () => void;
  } {
    let cancelled = false;
    
    const cancel = () => {
      cancelled = true;
    };

    const promise = new Promise<PaymentResult>(async (resolve) => {
      let attempts = 0;
      const maxAttempts = 60; // 3 minutes
      const intervalMs = 3000; // 3 seconds
      let lastStatus: 'Created' | 'Sent' | 'Paid' = 'Created';
      let amount = '';
      let reference = '';

      while (attempts < maxAttempts && !cancelled) {
        const status = await this.pollPaymentStatus(pollUrl);
        
        if (!status) {
          attempts++;
          await new Promise(resolve => setTimeout(resolve, intervalMs));
          continue;
        }

        amount = status.amount;
        reference = status.reference;
        
        // Call status update callback if provided
        if (onStatusUpdate) {
          onStatusUpdate(status);
        }

        console.log(`Payment polling attempt ${attempts + 1}: Status = ${status.status}`);

        // Check for successful payment
        if (status.status === 'Paid') {
          resolve({
            success: true,
            status: 'Paid',
            amount,
            reference,
          });
          return;
        }

        // Check for failed transaction (Created -> Sent -> Created)
        if (lastStatus === 'Sent' && status.status === 'Created') {
          resolve({
            success: false,
            status: 'Failed',
            amount,
            reference,
          });
          return;
        }

        lastStatus = status.status;
        attempts++;
        
        // Wait before next poll
        if (!cancelled) {
          await new Promise(resolve => setTimeout(resolve, intervalMs));
        }
      }

      // Timeout or cancelled
      resolve({
        success: false,
        status: cancelled ? 'Failed' : 'Timeout',
        amount,
        reference,
      });
    });

    return { promise, cancel };
  }
}
