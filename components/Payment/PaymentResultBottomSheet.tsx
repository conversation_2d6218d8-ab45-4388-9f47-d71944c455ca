import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { MaterialIcons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';
import { RenderBackdrop } from '../RenderBackdrop';

export interface PaymentResultBottomSheetHandle {
  present: (result: PaymentResultData) => void;
  dismiss: () => void;
}

export interface PaymentResultData {
  success: boolean;
  type: 'ticket' | 'promotion';
  amount?: string;
  reference?: string;
  status: 'Paid' | 'Failed' | 'Timeout';
  eventTitle?: string;
  ticketType?: string;
  promotionPackage?: string;
  quantity?: number;
}

interface PaymentResultBottomSheetProps {
  onClose?: () => void;
}

const PaymentResultBottomSheet = forwardRef<
  PaymentResultBottomSheetHandle,
  PaymentResultBottomSheetProps
>(({ onClose }, ref) => {
  const { colors } = useColorScheme();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [result, setResult] = React.useState<PaymentResultData | null>(null);

  useImperativeHandle(ref, () => ({
    present: (resultData: PaymentResultData) => {
      setResult(resultData);
      bottomSheetRef.current?.expand();
    },
    dismiss: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const handleClose = () => {
    bottomSheetRef.current?.close();
    onClose?.();
  };

  const getIcon = () => {
    if (!result) return 'help';
    
    if (result.success) {
      return 'check-circle';
    } else if (result.status === 'Timeout') {
      return 'access-time';
    } else {
      return 'error';
    }
  };

  const getIconColor = () => {
    if (!result) return colors.grey;
    
    if (result.success) {
      return '#10b981'; // green
    } else if (result.status === 'Timeout') {
      return '#f59e0b'; // yellow
    } else {
      return '#ef4444'; // red
    }
  };

  const getTitle = () => {
    if (!result) return '';
    
    if (result.success) {
      return result.type === 'ticket' ? 'Ticket Purchase Successful!' : 'Event Promotion Activated!';
    } else if (result.status === 'Timeout') {
      return 'Payment Timeout';
    } else {
      return result.type === 'ticket' ? 'Ticket Purchase Failed' : 'Event Promotion Failed';
    }
  };

  const getMessage = () => {
    if (!result) return '';
    
    if (result.success) {
      if (result.type === 'ticket') {
        const ticketText = result.quantity && result.quantity > 1 ? 'tickets' : 'ticket';
        return `Successfully purchased ${result.quantity || 1} ${result.ticketType || ''} ${ticketText} for ${result.eventTitle || 'the event'}.`;
      } else {
        return `Your ${result.promotionPackage || ''} promotion package has been activated for ${result.eventTitle || 'your event'}.`;
      }
    } else if (result.status === 'Timeout') {
      return 'The payment process timed out. Please try again or contact support if the issue persists.';
    } else {
      if (result.type === 'ticket') {
        return `Ticket purchase for ${result.amount || 'amount'} failed. Please try again or use a different payment method.`;
      } else {
        return `Promotion payment for ${result.amount || 'amount'} failed. Please try again or use a different payment method.`;
      }
    }
  };

  const getSubMessage = () => {
    if (!result) return '';
    
    if (result.success && result.reference) {
      return `Reference: ${result.reference}`;
    } else if (!result.success && result.status === 'Failed') {
      return 'No charges were made to your account.';
    }
    return '';
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      snapPoints={['50%']}
      enablePanDownToClose
      onClose={handleClose}
      backgroundStyle={{ backgroundColor: colors.background }}
      backdropComponent={RenderBackdrop}
      handleIndicatorStyle={{ backgroundColor: colors.foreground }}>
      <BottomSheetView style={{ flex: 1, padding: 20 }}>
        {result && (
          <>
            {/* Icon */}
            <View className="items-center mb-6">
              <View
                className="w-20 h-20 rounded-full items-center justify-center"
                style={{ backgroundColor: `${getIconColor()}20` }}>
                <MaterialIcons name={getIcon() as any} size={40} color={getIconColor()} />
              </View>
            </View>

            {/* Title */}
            <Text
              className="text-2xl font-bold text-center mb-4"
              style={{ color: colors.foreground }}>
              {getTitle()}
            </Text>

            {/* Message */}
            <Text
              className="text-base text-center mb-2"
              style={{ color: colors.foreground }}>
              {getMessage()}
            </Text>

            {/* Sub Message */}
            {getSubMessage() && (
              <Text
                className="text-sm text-center mb-6"
                style={{ color: colors.grey }}>
                {getSubMessage()}
              </Text>
            )}

            {/* Amount Display */}
            {result.amount && (
              <View className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 mb-6">
                <Text
                  className="text-center text-lg font-medium"
                  style={{ color: colors.foreground }}>
                  Amount: ${result.amount}
                </Text>
              </View>
            )}

            {/* Action Button */}
            <TouchableOpacity
              className="w-full py-4 rounded-full"
              style={{ backgroundColor: colors.primary }}
              onPress={handleClose}>
              <Text className="text-white text-lg font-bold text-center">
                {result.success ? 'Continue' : 'Try Again'}
              </Text>
            </TouchableOpacity>
          </>
        )}
      </BottomSheetView>
    </BottomSheet>
  );
});

PaymentResultBottomSheet.displayName = 'PaymentResultBottomSheet';

export default PaymentResultBottomSheet;
