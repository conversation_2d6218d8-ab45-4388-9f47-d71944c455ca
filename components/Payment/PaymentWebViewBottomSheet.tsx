import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { WebView } from 'react-native-webview';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useColorScheme } from '~/lib/useColorScheme';
import { RenderBackdrop } from '../RenderBackdrop';

export interface PaymentWebViewBottomSheetHandle {
  present: (url: string, onClose?: () => void) => void;
  dismiss: () => void;
}

interface PaymentWebViewBottomSheetProps {
  onNavigationStateChange?: (navState: any) => void;
  onClose?: () => void;
}

const PaymentWebViewBottomSheet = forwardRef<
  PaymentWebViewBottomSheetHandle,
  PaymentWebViewBottomSheetProps
>(({ onNavigationStateChange, onClose }, ref) => {
  const { colors } = useColorScheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const webViewRef = useRef<WebView>(null);
  const [paymentUrl, setPaymentUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [canGoBack, setCanGoBack] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  const [onCloseCallback, setOnCloseCallback] = useState<(() => void) | undefined>();

  useImperativeHandle(ref, () => ({
    present: (url: string, closeCallback?: () => void) => {
      setPaymentUrl(url);
      setOnCloseCallback(() => closeCallback);
      setIsLoading(true);
      setCanGoBack(false);
      setCurrentUrl('');
      bottomSheetRef.current?.expand();
    },
    dismiss: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const handleClose = () => {
    Alert.alert(
      'Close Payment',
      'Are you sure you want to close the payment? Your transaction may not be completed.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Close',
          style: 'destructive',
          onPress: () => {
            bottomSheetRef.current?.close();
            onCloseCallback?.();
            onClose?.();
          },
        },
      ]
    );
  };

  const handleGoBack = () => {
    if (canGoBack && webViewRef.current) {
      webViewRef.current.goBack();
    }
  };

  const handleRefresh = () => {
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  const handleWebViewNavigationStateChange = (navState: any) => {
    setCanGoBack(navState.canGoBack);
    setCurrentUrl(navState.url);
    setIsLoading(navState.loading);
    
    // Call the external navigation state change handler
    onNavigationStateChange?.(navState);
    
    // Check if payment is completed (you might want to customize this logic)
    if (navState.url.includes('success') || navState.url.includes('completed')) {
      // Payment might be completed, but let the polling handle the actual verification
      console.log('Payment URL suggests completion:', navState.url);
    }
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={-1}
      snapPoints={['95%']}
      enablePanDownToClose={false}
      backgroundStyle={{ backgroundColor: colors.background }}
      backdropComponent={RenderBackdrop}
      handleIndicatorStyle={{ backgroundColor: colors.foreground }}>
      <BottomSheetView style={{ flex: 1 }}>
        {/* Header */}
        <View
          className="flex-row items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700"
          style={{ paddingTop: insets.top }}>
          <View className="flex-row items-center flex-1">
            <TouchableOpacity
              onPress={handleGoBack}
              disabled={!canGoBack}
              className={`mr-3 p-2 rounded-full ${canGoBack ? '' : 'opacity-50'}`}>
              <MaterialIcons 
                name="arrow-back" 
                size={24} 
                color={canGoBack ? colors.foreground : colors.grey} 
              />
            </TouchableOpacity>
            
            <View className="flex-1">
              <Text 
                className="text-lg font-bold" 
                style={{ color: colors.foreground }}
                numberOfLines={1}>
                Payment
              </Text>
              {currentUrl && (
                <Text 
                  className="text-xs" 
                  style={{ color: colors.grey }}
                  numberOfLines={1}>
                  {currentUrl}
                </Text>
              )}
            </View>
          </View>

          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={handleRefresh}
              className="mr-3 p-2 rounded-full">
              <MaterialIcons name="refresh" size={24} color={colors.foreground} />
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={handleClose}
              className="p-2 rounded-full">
              <MaterialIcons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Loading Indicator */}
        {isLoading && (
          <View className="absolute top-20 left-0 right-0 z-10 items-center">
            <View className="bg-white dark:bg-gray-800 px-4 py-2 rounded-full shadow-lg">
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          </View>
        )}

        {/* WebView */}
        <View className="flex-1">
          {paymentUrl ? (
            <WebView
              ref={webViewRef}
              source={{ uri: paymentUrl }}
              onNavigationStateChange={handleWebViewNavigationStateChange}
              onLoadStart={() => setIsLoading(true)}
              onLoadEnd={() => setIsLoading(false)}
              onError={(syntheticEvent) => {
                const { nativeEvent } = syntheticEvent;
                console.error('WebView error: ', nativeEvent);
                Alert.alert(
                  'Loading Error',
                  'Failed to load the payment page. Please try again.',
                  [
                    { text: 'Retry', onPress: handleRefresh },
                    { text: 'Close', onPress: handleClose },
                  ]
                );
              }}
              startInLoadingState={true}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              allowsInlineMediaPlayback={true}
              mediaPlaybackRequiresUserAction={false}
              style={{ flex: 1 }}
            />
          ) : (
            <View className="flex-1 items-center justify-center">
              <Text style={{ color: colors.grey }}>No payment URL provided</Text>
            </View>
          )}
        </View>
      </BottomSheetView>
    </BottomSheet>
  );
});

PaymentWebViewBottomSheet.displayName = 'PaymentWebViewBottomSheet';

export default PaymentWebViewBottomSheet;
