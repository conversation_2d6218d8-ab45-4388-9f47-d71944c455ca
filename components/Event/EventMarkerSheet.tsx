import { Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { format } from 'date-fns';
import { useRouter, useFocusEffect } from 'expo-router';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Text, View, Image, TouchableOpacity, Linking, Platform, BackHandler } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import LocationPreview from '../Map/LocationPreview';
import PersonProfileSheet, { PersonProfileSheetHandle } from '../People/PersonProfileSheet';
import PhotoViewModal from '../Profile/PhotoViewModal';
import { RenderBackdrop } from '../RenderBackdrop';

import { useColorScheme } from '~/lib/useColorScheme';
import { getEventStatus } from '~/lib/eventStatusUtils';
import { useEvent } from '~/providers/MapProvider';
import { EventService } from '~/services/EventService';
import { UserStore } from '~/store/store';
import { EventType } from '~/types';

// Ticket Info Component
const TicketsContent: React.FC<{
  event: EventType;
  onBack: () => void;
}> = ({ event, onBack }) => {
  const { colors } = useColorScheme();

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'EEEE, MMMM d');
  };

  const formatTime = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return format(date, 'h:mm a');
  };

  const getTicketInfo = () => {
    if (!event) return null;

    if (!event.isPaid) {
      return {
        isFree: true,
        price: 'Free',
        totalTickets: event.ticketSetup.totalTickets,
      };
    }

    if (event.ticketSetup.hasLevels) {
      return {
        isFree: false,
        hasLevels: true,
        levels: event.ticketSetup.levels,
      };
    }

    return {
      isFree: false,
      hasLevels: false,
      price: event.ticketSetup.price,
      totalTickets: event.ticketSetup.totalTickets,
    };
  };

  const ticketInfo = getTicketInfo() || { isFree: true, price: 0, totalTickets: 0 };

  return (
    <BottomSheetScrollView contentContainerStyle={{ paddingBottom: 20 }}>
      <View className="p-4">
        {/* Back Button */}
        <TouchableOpacity className="flex-row items-center mb-4" onPress={onBack}>
          <MaterialIcons name="arrow-back" size={24} color={colors.foreground} />
          <Text className="ml-2 text-base font-medium " style={{ color: colors.foreground }}>
            Back to Event
          </Text>
        </TouchableOpacity>

        <View className="mb-6">
          <Text className="mb-1 text-2xl font-bold light:text-light-text dark:text-dark-text">
            {event?.title}
          </Text>
          <Text className="text-base font-medium light:text-light-text/70 dark:text-dark-text/70">
            {formatDate(event?.startDateTime)} • {formatTime(event?.startDateTime)} to{' '}
            {formatTime(event?.endDateTime)}
          </Text>
        </View>

        <View className="mb-6">
          <Text className="mb-3 text-lg font-bold light:text-light-text dark:text-dark-text">
            Registration
          </Text>
          <Text className="mb-4 text-base light:text-light-text dark:text-dark-text">
            {ticketInfo?.isFree
              ? 'Hello! To join the event, please register below.'
              : 'Select your ticket type to continue with your purchase.'}
          </Text>

          {ticketInfo?.isFree ? (
            <TouchableOpacity className="py-3 rounded-lg bg-light-primary dark:bg-dark-primary">
              <Text className="text-base font-bold text-center text-white">Register</Text>
            </TouchableOpacity>
          ) : ticketInfo?.hasLevels ? (
            <View className="space-y-3">
              {ticketInfo.levels?.map((level: any, index: number) => (
                <View
                  key={index}
                  className="p-4 border border-gray-200 rounded-lg dark:border-gray-700">
                  <View className="flex-row items-center justify-between mb-2">
                    <Text className="text-base font-bold light:text-light-text dark:text-dark-text">
                      {level.type}
                    </Text>
                    <Text className="text-base font-bold light:text-light-primary dark:text-dark-primary">
                      {event.currency} {level.price}
                    </Text>
                  </View>
                  <Text className="mb-3 text-sm light:text-light-text/70 dark:text-dark-text/70">
                    {level.quantity} tickets available
                  </Text>
                  <TouchableOpacity className="py-2 rounded-lg bg-light-primary dark:bg-dark-primary">
                    <Text className="font-bold text-center text-white">Select</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <View className="p-4 mb-4 border border-gray-200 rounded-lg dark:border-gray-700">
              <View className="flex-row items-center justify-between mb-2">
                <Text className="text-base font-bold light:text-light-text dark:text-dark-text">
                  Standard Ticket
                </Text>
                <Text className="text-base font-bold light:text-light-primary dark:text-dark-primary">
                  {event.currency} {ticketInfo.price}
                </Text>
              </View>
              <Text className="mb-3 text-sm light:text-light-text/70 dark:text-dark-text/70">
                {ticketInfo.totalTickets} tickets available
              </Text>
              <TouchableOpacity className="py-2 rounded-lg bg-light-primary dark:bg-dark-primary">
                <Text className="font-bold text-center text-white">Buy Ticket</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {!ticketInfo?.isFree && (
          <View className="flex-row items-start gap-4 mb-4">
            <MaterialCommunityIcons name="shield" size={24} color={colors.foreground} />
            <View className="flex-1">
              <Text className="font-medium light:text-light-text text-subtitle dark:text-dark-text">
                Buyer Guarantee Protected
              </Text>
              <Text className="font-regular light:text-light-text/70 text-body dark:text-dark-text/70">
                Every ticket is protected. If your event gets canceled, we'll make it right.
              </Text>
            </View>
          </View>
        )}
      </View>
    </BottomSheetScrollView>
  );
};

const EventMarkerSheetContent: React.FC = () => {
  const { selectedEvent, setSelectedEvent } = useEvent();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);
  const { colors } = useColorScheme();
  const [showTickets, setShowTickets] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savedEvents, setSavedEvents] = useState<string[]>([]);
  const [photoModalVisible, setPhotoModalVisible] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<any>(null);
  const insets = useSafeAreaInsets();
  const router = useRouter();

  // Get current user from store
  const currentUser = UserStore((state: any) => state.user);

  // Function to open Google Maps
  const openInGoogleMaps = () => {
    if (!selectedEvent?.locationData?.coordinates) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Location coordinates not available',
        position: 'bottom',
      });
      return;
    }

    // Coordinates are stored as [longitude, latitude]
    const [longitude, latitude] = selectedEvent.locationData.coordinates;
    const address = selectedEvent.locationData.address || selectedEvent.location || '';

    let url: string;

    if (Platform.OS === 'ios') {
      // For iOS, use Apple Maps or Google Maps app
      url = `maps://app?daddr=${latitude},${longitude}`;
      // Alternative Google Maps URL for iOS: `comgooglemaps://?daddr=${latitude},${longitude}`
    } else {
      // For Android, use Google Maps
      url = `geo:${latitude},${longitude}?q=${latitude},${longitude}(${encodeURIComponent(address)})`;
    }

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // Fallback to web Google Maps
          const webUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => {
        console.error('Error opening maps:', err);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Could not open maps application',
          position: 'bottom',
        });
      });
  };

  // Fetch saved events for the user
  useEffect(() => {
    const fetchSavedEvents = async () => {
      if (currentUser?.id) {
        try {
          const response = await EventService.getSavedEvents(currentUser.id);
          if (response.success && response.body) {
            // Extract event IDs from the saved events

            const eventIds = response.body.events.map(
              (event: any) => event.id || event.eventId || event._id
            );
            setSavedEvents(eventIds);
          }
        } catch (error) {
          console.error('Error fetching saved events:', error);
        }
      }
    };

    fetchSavedEvents();
  }, [currentUser?.id]);

  // Check if the selected event is saved when selectedEvent or savedEvents change
  useEffect(() => {
    if (selectedEvent && savedEvents.length > 0) {
      const isEventSaved = savedEvents.includes(selectedEvent.id.toString());
      setIsSaved(isEventSaved);
    } else {
      setIsSaved(false);
    }
  }, [selectedEvent, savedEvents]);

  // Handle save/unsave event
  const handleSaveEvent = async () => {
    if (!selectedEvent || !currentUser?.id) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Unable to save event. Please try again.',
        position: 'bottom',
      });
      return;
    }

    setIsSaving(true);
    try {
      await EventService.saveEvent(currentUser.id, selectedEvent.id.toString());

      // Update local saved events state
      if (isSaved) {
        // Remove from saved events
        setSavedEvents((prev) => prev.filter((id) => id !== selectedEvent.id.toString()));
      } else {
        // Add to saved events
        setSavedEvents((prev) => [...prev, selectedEvent.id.toString()]);
      }

      setIsSaved(!isSaved);

      /*  Toast.show({
        type: 'success',
        text1: isSaved ? 'Event Unsaved' : 'Event Saved',
        text2: isSaved
          ? 'Event removed from your saved events'
          : 'Event added to your saved events',
        position: 'bottom',
      }); */
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to save event. Please try again.',
        position: 'bottom',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePhotoPress = (photo: any) => {
    setSelectedPhoto(photo);
    setPhotoModalVisible(true);
  };

  const handleEventImagePress = () => {
    if (selectedEvent) {
      const imageData = {
        id: selectedEvent.id || 'event-image',
        secureUrl: getEventImage(),
        publicId: selectedEvent.coverImage || 'event-cover',
      };
      handlePhotoPress(imageData);
    }
  };

  useEffect(() => {
    if (selectedEvent) {
      console.log(selectedEvent);
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.close();
      setShowTickets(false);
    }
  }, [selectedEvent]);

  // Handle back button to close the sheet when it's open
  useEffect(() => {
    const handleBackPress = () => {
      if (selectedEvent) {
        // Close the bottom sheet
        setSelectedEvent(null);
        setShowTickets(false);
        return true; // Prevent default back behavior
      }
      return false; // Let default back behavior happen
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      backHandler.remove();
    };
  }, [selectedEvent]);

  const handleSheetChanges = (index: number) => {
    if (index === -1) {
      setSelectedEvent(null);
      setShowTickets(false);
    }
  };

  const getEventImage = () => {
    // Use a default image if no cover image is provided
    if (!selectedEvent?.coverImage) {
      const eventTypeImages: Record<string, string> = {
        Business:
          'https://images.unsplash.com/photo-1565398305935-49e5dcc5c9f6?q=80&w=1920&auto=format',
        Leisure:
          'https://images.unsplash.com/photo-1517457373958-b7bdd4587205?q=80&w=1920&auto=format',
        Entertainment:
          'https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?q=80&w=1920&auto=format',
        Educational:
          'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1920&auto=format',
      };
      const eventType = selectedEvent?.eventType || '';
      return (
        eventTypeImages[eventType] || 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4'
      );
    }
    return selectedEvent.coverImage;
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      index={selectedEvent ? 0 : -1}
      snapPoints={['100%']}
      enablePanDownToClose
      onClose={() => {
        setSelectedEvent(null);
        setShowTickets(false);
      }}
      onChange={handleSheetChanges}
      backgroundStyle={{ backgroundColor: colors.background }}
      enableOverDrag={false}
      backdropComponent={RenderBackdrop}
      containerStyle={{
        zIndex: 100,
        elevation: 100,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
      handleIndicatorStyle={{
        backgroundColor: colors.foreground,
      }}
      topInset={insets.top}
      bottomInset={0}>
      {showTickets && selectedEvent ? (
        <TicketsContent event={selectedEvent} onBack={() => setShowTickets(false)} />
      ) : (
        <BottomSheetScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}>
          <View
            className="flex-row items-center justify-between px-4 py-4 border-b"
            style={{ borderBottomColor: colors.grey5 }}>
            <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
              Event Details
            </Text>
            <TouchableOpacity
              className="p-2"
              onPress={() => {
                setSelectedEvent(null);
                setShowTickets(false);
              }}>
              <Ionicons name="close" size={24} color={colors.foreground} />
            </TouchableOpacity>
          </View>

          {/* Event Header Image */}
          <TouchableOpacity
            className="relative w-full h-48"
            onPress={handleEventImagePress}
            activeOpacity={0.9}>
            <Image
              source={{ uri: getEventImage() }}
              className="w-full h-full"
              style={{ resizeMode: 'cover' }}
            />

            {/* Event Status Chip */}
            {(() => {
              const eventStatus = getEventStatus(
                selectedEvent?.startDateTime,
                selectedEvent?.endDateTime
              );

              if (!eventStatus.status) return null;

              return (
                <View
                  className="absolute px-2 py-1 rounded-full right-2 top-2"
                  style={{ backgroundColor: eventStatus.backgroundColor }}>
                  <Text className="text-xs font-bold text-white">{eventStatus.status}</Text>
                </View>
              );
            })()}

            <View className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent">
              <Text className="mb-1 text-2xl font-bold text-white uppercase">
                {selectedEvent?.title}
              </Text>
              <Text className="text-base text-white">{selectedEvent?.location}</Text>
            </View>
          </TouchableOpacity>

          {/* Event Details */}
          <View className="p-4">
            <Text className="mb-4 text-2xl font-bold light:text-light-text dark:text-dark-text">
              {selectedEvent?.location} - {selectedEvent?.title}
            </Text>

            {/* Date and Time */}
            <View className="flex-row items-center justify-between mb-6">
              <View className="flex-1">
                <Text className="text-lg font-medium light:text-light-text dark:text-dark-text">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'EEEE')
                    : ''}
                </Text>

                <Text className="text-base light:text-light-text/70 dark:text-dark-text/70">
                  {selectedEvent?.startDateTime
                    ? format(new Date(selectedEvent.startDateTime), 'MMMM d, h:mm a')
                    : ''}{' '}
                  to{' '}
                  {selectedEvent?.endDateTime
                    ? format(new Date(selectedEvent.endDateTime), 'h:mm a')
                    : ''}
                </Text>
                <Text className="mb-2 text-base light:text-light-text/70 dark:text-dark-text/70">
                  {selectedEvent?.locationData.address || selectedEvent?.location}
                </Text>
              </View>
              <View className="items-end" />
            </View>

            {/* Location */}
            <View className="mb-6">
              {selectedEvent?.locationData && (
                <LocationPreview
                  location={{
                    coordinates: selectedEvent.locationData.coordinates,
                    manualAddress: selectedEvent.locationData.address,
                    name: selectedEvent.locationData.name,
                    address: selectedEvent.locationData.address || '',
                  }}
                  editable={false}
                />
              )}

              {/* View Full Map Button */}
              {selectedEvent?.locationData?.coordinates && (
                <TouchableOpacity
                  onPress={openInGoogleMaps}
                  className="flex-row items-center justify-center px-4 py-2 mt-3 border border-gray-300 rounded-lg dark:border-gray-600"
                  style={{ backgroundColor: 'transparent' }}>
                  <MaterialIcons
                    name="map"
                    size={18}
                    color={colors.primary}
                    style={{ marginRight: 8 }}
                  />
                  <Text className="text-sm font-medium" style={{ color: colors.primary }}>
                    View Full Map
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Registration Section */}
            <View className="mb-6">
              <View className="flex-row gap-3">
                <TouchableOpacity
                  onPress={() => setShowTickets(true)}
                  className="flex-row items-center justify-center flex-1 px-4 py-3 rounded-full bg-light-primary dark:bg-dark-primary">
                  <MaterialIcons
                    name="how-to-reg"
                    size={20}
                    color="white"
                    style={{ marginRight: 8 }}
                  />
                  <Text className="text-base font-bold text-center text-white">
                    {selectedEvent?.isPaid ? 'View Tickets' : 'Register'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleSaveEvent}
                  disabled={isSaving}
                  className="flex-row items-center justify-center px-4 py-3 border-2 rounded-full"
                  style={{
                    borderColor: colors.primary,
                    backgroundColor: isSaved ? colors.primary : 'transparent',
                    opacity: isSaving ? 0.7 : 1,
                  }}>
                  <Ionicons
                    name={isSaved ? 'bookmark' : 'bookmark-outline'}
                    size={20}
                    color={isSaved ? 'white' : colors.primary}
                  />
                  <Text
                    className="ml-2 text-sm font-bold"
                    style={{ color: isSaved ? 'white' : colors.primary }}>
                    {isSaving ? 'Saving...' : isSaved ? 'Saved' : 'Save'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* About Event Section */}
            <View className="mb-6">
              <Text className="mb-2 text-lg font-bold light:text-light-text dark:text-dark-text">
                About Event
              </Text>
              <Text className="text-base light:text-light-text dark:text-dark-text">
                {selectedEvent?.description}
              </Text>
            </View>

            {/* Event Uploads Section */}
            {(selectedEvent as any)?.eventUploads &&
              (selectedEvent as any).eventUploads.length > 0 && (
                <View className="mb-6">
                  <Text className="mb-3 text-lg font-bold light:text-light-text dark:text-dark-text">
                    Event Photos
                  </Text>
                  <View className="flex-row flex-wrap -mx-1">
                    {(selectedEvent as any).eventUploads.map((upload: any, index: number) => (
                      <TouchableOpacity
                        key={upload.id || index}
                        className="w-1/3 px-1 mb-2"
                        onPress={() => handlePhotoPress(upload)}>
                        <Image
                          source={{ uri: upload.secureUrl }}
                          className="w-full rounded-lg aspect-square"
                          style={{ resizeMode: 'cover' }}
                        />
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}

            {/* Hosts Section */}
            <View className="mb-6">
              <Text className="mb-3 text-lg font-bold light:text-light-text dark:text-dark-text">
                Host
              </Text>

              <TouchableOpacity
                className="flex-row items-center mb-2"
                onPress={() => {
                  // Type assertion to handle extended event properties
                  const eventWithUser = selectedEvent as any;
                  if (eventWithUser?.user?.id && eventWithUser?.user?.email) {
                    // Close the current bottom sheet first
                    setSelectedEvent(null);
                    setShowTickets(false);
                    // Navigate to view profile screen
                    router.push({
                      pathname: '/Auth/viewProfile',
                      params: {
                        userId: eventWithUser.user.id.toString(),
                        email: eventWithUser.user.email.toString(),
                      },
                    });
                  }
                }}>
                <Image
                  source={{
                    uri:
                      (selectedEvent as any)?.user?.profilePicture &&
                      Array.isArray((selectedEvent as any)?.user.profilePicture) &&
                      (selectedEvent as any)?.user.profilePicture.length > 0 &&
                      (selectedEvent as any)?.user.profilePicture[
                        (selectedEvent as any)?.user.profilePicture.length - 1
                      ]?.secureUrl
                        ? (selectedEvent as any)?.user.profilePicture[
                            (selectedEvent as any)?.user.profilePicture.length - 1
                          ].secureUrl
                        : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                  }}
                  className="w-10 h-10 mr-3 rounded-full"
                  style={{ resizeMode: 'cover' }}
                />
                <View className="flex-1">
                  <Text className="text-base font-medium light:text-light-text dark:text-dark-text">
                    {(selectedEvent as any)?.user?.fullName || 'Event Host'}
                  </Text>
                  <View className="flex-row items-center mt-1">
                    {/* Display 5 stars with random rating for demo purposes */}
                    {[1, 2, 3, 4, 5].map((star) => {
                      // Generate a random rating between 3-5 for demo
                      const rating = 3 + Math.floor(Math.random() * 2.1);
                      return (
                        <Ionicons
                          key={star}
                          name={star <= rating ? 'star' : 'star-outline'}
                          size={14}
                          color="#FFD700"
                          style={{ marginRight: 2 }}
                        />
                      );
                    })}
                    <Text className="ml-1 text-xs light:text-light-text/70 dark:text-dark-text/70">
                      {(3 + Math.random() * 2).toFixed(1)}/5.0
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={colors.grey}
                  style={{ marginLeft: 8 }}
                />
              </TouchableOpacity>
            </View>
          </View>
        </BottomSheetScrollView>
      )}
      <PersonProfileSheet ref={personProfileSheetRef} />
      <PhotoViewModal
        visible={photoModalVisible}
        photo={selectedPhoto}
        onClose={() => setPhotoModalVisible(false)}
      />
    </BottomSheet>
  );
};

export default function EventMarkerSheet() {
  return (
    <View
      style={{
        position: 'absolute',
        zIndex: 100,
        width: '100%',
        height: '100%',
        pointerEvents: 'box-none',
      }}>
      <EventMarkerSheetContent />
    </View>
  );
}
